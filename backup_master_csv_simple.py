#!/usr/bin/env python3
"""
Simple CSV Backup Script for <PERSON> Bounces and Unsubs Directory
Creates a ZIP backup of all CSV files with date stamp
"""

import os
import zipfile
import shutil
from datetime import date, datetime
import glob

def backup_csv_files():
    """Simple function to backup CSV files to a ZIP archive."""
    
    # Source directory
    source_dir = r"H:\<PERSON> Bounces and Unsubs\Master Bounces and Unsubs"
    
    # Check if source directory exists
    if not os.path.exists(source_dir):
        print(f"Error: Source directory not found: {source_dir}")
        return False
    
    # Get current date for filename
    current_date = date.today()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    date_str = current_date.strftime("%Y-%m-%d")
    
    # Find all CSV files
    os.chdir(source_dir)
    csv_files = glob.glob("*.csv")
    
    if not csv_files:
        print(f"No CSV files found in {source_dir}")
        return False
    
    print(f"Found {len(csv_files)} CSV files to backup:")
    for csv_file in csv_files:
        file_size = os.path.getsize(csv_file) / 1024  # Size in KB
        print(f"  - {csv_file} ({file_size:.1f} KB)")
    
    # Create ZIP filename
    zip_filename = f"Master_CSV_Backup_{date_str}.zip"
    zip_path = os.path.join(source_dir, zip_filename)
    
    print(f"\nCreating backup archive: {zip_filename}")
    
    try:
        # Create ZIP archive
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for csv_file in csv_files:
                print(f"  Adding {csv_file} to archive...")
                zipf.write(csv_file, csv_file)
        
        # Get final archive size
        archive_size = os.path.getsize(zip_path) / (1024 * 1024)  # Size in MB
        print(f"\n✓ Backup completed successfully!")
        print(f"Archive: {zip_path}")
        print(f"Archive size: {archive_size:.2f} MB")
        print(f"Files backed up: {len(csv_files)}")
        
        return True
        
    except Exception as e:
        print(f"Error creating backup: {str(e)}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("CSV Backup Script for Master Bounces and Unsubs")
    print("=" * 60)
    
    try:
        success = backup_csv_files()
        
        if success:
            print("\n" + "=" * 60)
            print("BACKUP COMPLETED SUCCESSFULLY!")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("BACKUP FAILED - Check error messages above")
            print("=" * 60)
            
    except KeyboardInterrupt:
        print("\n\nBackup interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
