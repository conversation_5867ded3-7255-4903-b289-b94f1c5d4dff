#!/usr/bin/env python3
"""
Simple CSV Backup Script for <PERSON> Bounces and Unsubs Directory
Creates a backup folder with all CSV files and date stamp
"""

import os
import shutil
from datetime import date, datetime
import glob

def backup_csv_files():
    """Simple function to backup CSV files to a backup folder."""

    # Source directory
    source_dir = r"H:\<PERSON> Bounces and Unsubs\Master Bounces and Unsubs"

    # Check if source directory exists
    if not os.path.exists(source_dir):
        print(f"Error: Source directory not found: {source_dir}")
        return False

    # Get current date for folder name
    current_date = date.today()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    date_str = current_date.strftime("%Y-%m-%d")

    # Create backup folder name
    backup_folder_name = f"CSV_Backup_{date_str}_{timestamp}"
    backup_folder_path = os.path.join(source_dir, backup_folder_name)

    # Find all CSV files
    os.chdir(source_dir)
    csv_files = glob.glob("*.csv")

    if not csv_files:
        print(f"No CSV files found in {source_dir}")
        return False

    print(f"Found {len(csv_files)} CSV files to backup:")
    for csv_file in csv_files:
        file_size = os.path.getsize(csv_file) / 1024  # Size in KB
        print(f"  - {csv_file} ({file_size:.1f} KB)")

    print(f"\nCreating backup folder: {backup_folder_name}")

    try:
        # Create backup folder
        os.makedirs(backup_folder_path, exist_ok=True)

        total_size = 0
        # Copy each CSV file to backup folder
        for csv_file in csv_files:
            source_file = os.path.join(source_dir, csv_file)
            backup_file = os.path.join(backup_folder_path, csv_file)

            print(f"  Copying {csv_file}...")
            shutil.copy2(source_file, backup_file)  # copy2 preserves metadata

            file_size = os.path.getsize(source_file)
            total_size += file_size

        # Calculate total backup size
        total_size_mb = total_size / (1024 * 1024)  # Size in MB

        print(f"\n✓ Backup completed successfully!")
        print(f"Backup folder: {backup_folder_path}")
        print(f"Total backup size: {total_size_mb:.2f} MB")
        print(f"Files backed up: {len(csv_files)}")

        return True

    except Exception as e:
        print(f"Error creating backup: {str(e)}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("CSV Backup Script for Master Bounces and Unsubs")
    print("=" * 60)
    print("This script creates a backup folder with all CSV files")
    print("=" * 60)

    try:
        success = backup_csv_files()

        if success:
            print("\n" + "=" * 60)
            print("BACKUP COMPLETED SUCCESSFULLY!")
            print("All CSV files have been copied to the backup folder.")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("BACKUP FAILED - Check error messages above")
            print("=" * 60)

    except KeyboardInterrupt:
        print("\n\nBackup interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")

    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
