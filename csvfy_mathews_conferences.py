#!/usr/bin/env python3
"""
Mathews Conferences CSV Processing Script

This script processes CSV files for Mathews conference data, extracting conference segment names,
filtering data using specific Mathews directory structure, and preparing output files.
"""

import os
import glob
import pandas as pd
import numpy as np
import re
import random
import warnings
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# Suppress SettingWithCopyWarning
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

class MathewsConferenceProcessor:
    def __init__(self):
        self.console = Console()
        
        # Base paths for Mathews structure
        self.master_dir = r"H:\Master Bounces and Unsubs"
        self.mathews_dir = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs"
        self.master_bounces_dir = os.path.join(self.master_dir, "Master Bounces and Unsubs")
        
    def create_progress_bar(self):
        """Create a progress bar with gradient colors"""
        return Progress(
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
            "[progress.percentage]{task.percentage:>3.0f}%",
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        )
    
    def display_header(self, title):
        """Display a formatted header"""
        header_text = Text(title, style="bold magenta")
        self.console.print(Panel(header_text, expand=False))
    
    def extract_conference_name(self, path: str) -> str:
        """
        Extract conference segment name from a path using regex.
        
        Args:
            path: Path string to extract from
            
        Returns:
            Conference name or prompts for manual input if not found
        """
        # Define a regex pattern to match the desired segment, including spaces
        pattern = r"\\([\w\s-]+\d{4})\\?"
        
        # Search for the pattern in the path
        match = re.search(pattern, path)
        
        if match:
            csn = match.group(1)
            self.console.print(f"[green]Conference Segment Name (CSN): {csn}[/green]")
            return csn
        else:
            self.console.print("[yellow]Desired segment not found in path.[/yellow]")
            
            # Prompt for manual input
            self.console.print("[blue]Please enter the conference segment name manually[/blue]")
            csn = input("Conference segment name (e.g., 'Conference 2023'): ").strip()
            
            if csn:
                self.console.print(f"[green]Using manually entered segment: {csn}[/green]")
                return csn
            else:
                self.console.print("[red]No segment name provided. Cannot proceed.[/red]")
                raise ValueError("No conference segment name provided")
    
    def load_filter_files(self, csn: str) -> tuple:
        """
        Load all filter files for the specified conference.
        
        Args:
            csn: Conference segment name
            
        Returns:
            Tuple of DataFrames (hard_bounces, unsubscribers_combined, replied_bounces)
        """
        self.console.print("[yellow]Loading filter files...[/yellow]")

        try:
            # Load Master Hard Bounces (unchanged)
            hb_path = os.path.join(self.master_bounces_dir, "Master_Hardbounces.csv")
            if os.path.exists(hb_path):
                df_hb = pd.read_csv(hb_path, on_bad_lines='skip')
                self.console.print(f"[green]✓ Loaded Master Hard Bounces: {len(df_hb)} records[/green]")
            else:
                df_hb = pd.DataFrame(columns=['Email'])
                self.console.print("[yellow]⚠ Master Hard Bounces file not found[/yellow]")
            
            # Load Global Unsubscribers from both CMS and Postpanel
            global_unsubs_files = [
                os.path.join(self.mathews_dir, "cms", "merged", "separated", "Global_Unsubscriber.csv"),
                os.path.join(self.mathews_dir, "postpanel", "merged", "separated", "Global_Unsubscriber.csv")
            ]
            
            global_unsubs_dfs = []
            for file_path in global_unsubs_files:
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path, on_bad_lines='skip')
                    global_unsubs_dfs.append(df)
                    self.console.print(f"[green]✓ Loaded Global Unsubscribers: {os.path.basename(file_path)} - {len(df)} records[/green]")
                else:
                    self.console.print(f"[yellow]⚠ Global Unsubscribers file not found: {os.path.basename(file_path)}[/yellow]")
            
            # Load Conference-specific Unsubscribers from both CMS and Postpanel
            conf_unsubs_files = [
                os.path.join(self.mathews_dir, "cms", "merged", "separated", f"{csn}_Unsubscribes.csv"),
                os.path.join(self.mathews_dir, "postpanel", "merged", "separated", f"{csn}_Unsubscribes.csv")
            ]
            
            conf_unsubs_dfs = []
            for file_path in conf_unsubs_files:
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path, on_bad_lines='skip')
                    conf_unsubs_dfs.append(df)
                    self.console.print(f"[green]✓ Loaded Conference Unsubscribers: {os.path.basename(file_path)} - {len(df)} records[/green]")
                else:
                    self.console.print(f"[yellow]⚠ Conference Unsubscribers file not found: {os.path.basename(file_path)}[/yellow]")
            
            # Combine all unsubscribers
            all_unsubs_dfs = global_unsubs_dfs + conf_unsubs_dfs
            if all_unsubs_dfs:
                df_unsubs_combined = pd.concat(all_unsubs_dfs, ignore_index=True)
                df_unsubs_combined['Email'] = df_unsubs_combined['Email'].str.lower()
                df_unsubs_combined.drop_duplicates(subset='Email', inplace=True)
                self.console.print(f"[green]✓ Combined Unsubscribers: {len(df_unsubs_combined)} unique records[/green]")
            else:
                df_unsubs_combined = pd.DataFrame(columns=['Email'])
                self.console.print("[yellow]⚠ No unsubscriber files found[/yellow]")
            
            # Load Replied Bounces
            replied_path = os.path.join(self.mathews_dir, "replied bounces", "separated", f"{csn}_replied_bouncers.csv")
            if os.path.exists(replied_path):
                df_replied = pd.read_csv(replied_path, on_bad_lines='skip')
                self.console.print(f"[green]✓ Loaded Replied Bounces: {len(df_replied)} records[/green]")
            else:
                df_replied = pd.DataFrame(columns=['Email'])
                self.console.print(f"[yellow]⚠ Replied Bounces file not found: {os.path.basename(replied_path)}[/yellow]")
            
            return df_hb, df_unsubs_combined, df_replied
            
        except Exception as e:
            self.console.print(f"[red]Error loading filter files: {str(e)}[/red]")
            return pd.DataFrame(columns=['Email']), pd.DataFrame(columns=['Email']), pd.DataFrame(columns=['Email'])
    
    def clean_name(self, name: str) -> str:
        """
        Clean name by removing text after the first comma.
        
        Args:
            name: Name string to clean
            
        Returns:
            Cleaned name string
        """
        return str(name).split(',')[0].strip()
    
    def process_conference_data(self, path: str, csn: str) -> pd.DataFrame:
        """
        Process conference data from CSV files.
        
        Args:
            path: Path to the directory containing CSV files
            csn: Conference segment name
            
        Returns:
            Processed DataFrame
        """
        self.console.print(f"[yellow]Processing conference data for {csn}...[/yellow]")
        
        try:
            os.chdir(path)
            
            # Create processing directory
            process_dir = os.path.join(path, "processing")
            os.makedirs(process_dir, exist_ok=True)
            
            # List all CSV files
            csv_files = glob.glob('*.csv')
            if not csv_files:
                self.console.print(f"[red]No CSV files found in {path}[/red]")
                return pd.DataFrame()
            
            # Create progress bar for reading files
            with self.create_progress_bar() as progress:
                read_task = progress.add_task("Reading CSV files...", total=len(csv_files))
                
                # Read and concatenate all CSV files
                dfs = []
                for f in csv_files:
                    df = pd.read_csv(f, on_bad_lines='skip', low_memory=False, encoding='utf-8-sig')
                    dfs.append(df)
                    progress.advance(read_task)
                
                # Concatenate all dataframes
                df_concat = pd.concat(dfs, ignore_index=True)
                self.console.print(f"[green]Successfully read {len(df_concat)} records from {len(csv_files)} files[/green]")
            
            # Standardize column names
            df_concat.rename(columns={
                'Author Name': 'Name', 
                'Name ': 'Name',
                'col1': 'Name',
                'col2': 'Article Title', 
                'email': 'Email',
                'name': 'Name',
                'Title': 'Article Title'
            }, inplace=True)
            
            # Clean names by removing text after comma
            if 'Name' in df_concat.columns:
                df_concat['Name'] = df_concat['Name'].apply(self.clean_name)
            
            # Remove rows with empty Email
            df_concat.dropna(subset='Email', inplace=True)
            
            # Save unfiltered data
            unfiltered_path = os.path.join(process_dir, f"{csn}-merged_unfiltered.csv")
            df_concat.to_csv(unfiltered_path, mode='w+', encoding='utf-8-sig', index=False)
            
            # Load filter files
            df_hb, df_unsubs_combined, df_replied = self.load_filter_files(csn)
            
            # Read back the unfiltered data
            df_concat = pd.read_csv(unfiltered_path, low_memory=False)
            
            # Apply filters step by step
            self.console.print("[yellow]Applying filters...[/yellow]")

            original_count = len(df_concat)

            # Filter out hard bounces
            hard_bounce_emails = df_concat[df_concat['Email'].isin(df_hb['Email'])]
            df_hb_filtered = df_concat[~(df_concat['Email'].isin(df_hb['Email']))]
            hb_filtered_count = original_count - len(df_hb_filtered)

            # Filter out unsubscribers
            unsubscriber_emails = df_hb_filtered[df_hb_filtered['Email'].isin(df_unsubs_combined['Email'])]
            df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered['Email'].isin(df_unsubs_combined['Email']))]
            unsubs_filtered_count = len(df_hb_filtered) - len(df_unsubs_filtered)

            # Filter out replied bounces
            replied_bounce_emails = df_unsubs_filtered[df_unsubs_filtered['Email'].isin(df_replied['Email'])]
            df_final = df_unsubs_filtered[~(df_unsubs_filtered['Email'].isin(df_replied['Email']))]
            replied_filtered_count = len(df_unsubs_filtered) - len(df_final)

            # Display detailed filtering results
            self.console.print(f"[green]Filtering Results for {csn}:[/green]")
            self.console.print(f"  • Original records: {original_count:,}")

            # Show hard bounces details
            self.console.print(f"  • Hard bounces filtered: {hb_filtered_count:,}")
            if hb_filtered_count > 0:
                self.console.print(f"    [red]Hard bounce emails removed:[/red]")
                for email in hard_bounce_emails['Email'].tolist():
                    self.console.print(f"      - {email}")

            # Show unsubscribers details
            self.console.print(f"  • Unsubscribers filtered: {unsubs_filtered_count:,}")
            if unsubs_filtered_count > 0:
                self.console.print(f"    [yellow]Unsubscriber emails removed:[/yellow]")
                for email in unsubscriber_emails['Email'].tolist():
                    self.console.print(f"      - {email}")

            # Show replied bounces details
            self.console.print(f"  • Replied bounces filtered: {replied_filtered_count:,}")
            if replied_filtered_count > 0:
                self.console.print(f"    [blue]Replied bounce emails removed:[/blue]")
                for email in replied_bounce_emails['Email'].tolist():
                    self.console.print(f"      - {email}")

            self.console.print(f"  • Final records: {len(df_final):,}")
            
            # Extract only Name and Email columns
            if 'Name' in df_final.columns and 'Article Title' in df_final.columns:
                df_final = df_final[["Name", "Email", "Article Title"]].copy()
                df_final.rename(columns={'Article Title': 'Title'}, inplace=True)
            elif 'Name' in df_final.columns:
                df_final = df_final[["Name", "Email"]].copy()
            else:
                df_final = df_final[["Email"]].copy()
                df_final['Name'] = 'Colleague'
            
            # Clean data
            df_clean = df_final.replace(r'^\s*$', np.nan, regex=True)
            df_clean = df_clean.fillna('Colleague')
            
            # Remove duplicates
            result = df_clean.drop_duplicates(subset='Email')
            
            # Clean up temporary files
            if os.path.exists(unfiltered_path):
                os.remove(unfiltered_path)
            if os.path.exists(process_dir):
                os.rmdir(process_dir)
            
            self.console.print(f"[green]Processing completed: {len(result)} final records[/green]")
            return result
            
        except Exception as e:
            self.console.print(f"[red]Error processing conference data: {str(e)}[/red]")
            return pd.DataFrame()

    def generate_subject_lines(self, df: pd.DataFrame, csn: str) -> pd.DataFrame:
        """
        Generate random subject lines for each record.

        Args:
            df: DataFrame with records
            csn: Conference segment name

        Returns:
            DataFrame with added subject lines
        """
        self.console.print("[yellow]Generating subject lines...[/yellow]")

        # List of subject line templates for Mathews conferences
        subj_list = [
            'Invitation to Submit Abstract for Oral Presentation',
            'Invitation to Submit Abstracts for [CCT_CSNAME]',
            'Call for Papers: 20-Minute Oral Presentation at [CCT_CSNAME]',
            'Submit Your Abstract for [CCT_CSNAME]',
            'Oral Presentation Slots Available at [CCT_CSNAME]',
            'Join Us as a Presenter at [CCT_CSNAME]',
            'Abstract Submission for Oral Presentations at [CCT_CSNAME] is OPEN!',
            'Your Expertise Wanted: Call for 20-Minutes Oral Presentation',
            '[CCT_CSNAME]: Now Accepting Abstract Submissions for Oral Presentations',
            'Share Your Research at [CCT_CSNAME]',
            'Invitation to Submit Abstract for 20-Minute Oral Presentation',
            'Present Your Findings at [CCT_CSNAME]',
            'Call for Oral Presentation Abstracts at [CCT_CSNAME]',
            '[CCT_CSNAME]: Call for 20-Minute Oral Presentation Abstracts',
            'Call for 20-Minute Oral Presentation Abstracts Now Open!',
            'Be Part of the Program: Submit Your Abstract Now!',
            'Call for Abstracts: [CCT_CSNAME]',
            'Submit your Research Abstract for the [CCT_CSNAME]',
            'Abstract Submission Open: [CCT_CSNAME]',
            'Submit Your Abstracts for the [CCT_CSNAME]',
            'Invitation to Speak at the [CCT_CSNAME]',
            'Be Our Guest Speaker at the [CCT_CSNAME]',
            'Call for Speakers: [CCT_CSNAME]',
            'Discovering the Future of Technology at [CCT_CSNAME]',
            'The Ultimate Networking Opportunity: [CCT_CSNAME]',
            "Don't Miss Out on [CCT_CSNAME]: Exploring the Latest Trends and Technologies",
            'Join the Conversations at [CCT_CSNAME]: A Dynamic Forum for Ideas and Inspiration'
        ]

        # Replace placeholder with actual conference name
        formatted_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

        # Assign random subject lines
        df["Subject"] = pd.Series(
            random.choices(formatted_subj_list, k=len(df)),
            index=df.index
        )

        self.console.print(f"[green]Generated subject lines for {len(df)} records[/green]")
        return df

    def save_output_files(self, df: pd.DataFrame, csn: str, n_temps: int = 1, create_splits: bool = False, include_subject: bool = True) -> int:
        """
        Save output files, splitting the data if needed.

        Args:
            df: DataFrame with records
            csn: Conference segment name
            n_temps: Number of templates/chunks to split into
            create_splits: Whether to create splits of 10000 rows each in the splits folder
            include_subject: Whether to include the Subject column in the output

        Returns:
            int: Number of files created
        """
        self.console.print("[yellow]Saving output files...[/yellow]")

        try:
            # Create output directory
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)

            # Remove Subject column if not needed
            if not include_subject and 'Subject' in df.columns:
                self.console.print("[blue]Removing Subject column from output...[/blue]")
                df = df.drop(columns=['Subject'])

            # Define splits directory path
            splits_dir = os.path.join(output_dir, "splits")

            # Create splits directory only if we're using splits
            if create_splits:
                os.makedirs(splits_dir, exist_ok=True)

            # Get the number of rows in the dataframe
            n_rows = len(df)

            # Maximum rows per file (10,000 as per user preference)
            max_rows_per_file = 10000

            # Track total number of files created
            total_files = 0

            # Handle based on whether we're creating splits or using templates
            if create_splits:
                # Calculate number of splits needed
                num_splits = (n_rows + max_rows_per_file - 1) // max_rows_per_file

                # Create progress bar for splits
                with self.create_progress_bar() as progress:
                    splits_task = progress.add_task("Creating split files...", total=num_splits)

                    # Process each split
                    for i in range(num_splits):
                        start_idx = i * max_rows_per_file
                        end_idx = min((i + 1) * max_rows_per_file, n_rows)

                        # Extract chunk
                        chunk = df.iloc[start_idx:end_idx]

                        # Save to splits directory
                        output_path = os.path.join(splits_dir, f"{csn}_split_{i+1:03d}.csv")
                        chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                        total_files += 1
                        progress.advance(splits_task)

                self.console.print(f"[green]Created {num_splits} split files with {max_rows_per_file} rows each[/green]")

            else:
                # Calculate the size of each chunk based on templates
                chunk_size = n_rows // n_temps if n_temps > 0 else n_rows

                # Create progress bar for templates
                with self.create_progress_bar() as progress:
                    template_task = progress.add_task("Creating template files...", total=n_temps)

                    # Split the dataframe into chunks and save them as separate csv files
                    for i in range(n_temps):
                        start = i * chunk_size
                        end = (i + 1) * chunk_size if i < n_temps - 1 else n_rows
                        chunk = df[start:end]

                        # Save directly to output directory
                        output_path = os.path.join(output_dir, f"{csn}-{i+1}_Mathews.csv")
                        chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                        total_files += 1
                        progress.advance(template_task)

                self.console.print(f"[green]Created {n_temps} template files[/green]")

            # Show what columns were included
            columns_info = ", ".join(df.columns.tolist())
            self.console.print(f"[blue]Columns included: {columns_info}[/blue]")

            return total_files

        except Exception as e:
            self.console.print(f"[red]Error saving output files: {str(e)}[/red]")
            return 0

    def get_yes_no_input(self, prompt: str) -> bool:
        """
        Get a yes/no input from the user.

        Args:
            prompt: The prompt to display to the user

        Returns:
            True for yes, False for no
        """
        while True:
            response = input(f"{prompt} (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                self.console.print("[yellow]Please enter 'y' or 'n'.[/yellow]")

    def run_processing(self):
        """Main method to run the entire processing workflow"""
        try:
            # Display header
            self.display_header("Mathews Conferences CSV Processing Script")

            # Get input path
            path = input("Enter directory path (Loc): ").strip().strip('"\'')

            # Extract conference segment name
            self.console.print("[yellow]Extracting conference segment name...[/yellow]")
            csn = self.extract_conference_name(path)
            self.console.print(f"[blue]Processing conference: {csn}[/blue]")

            # Process conference data
            self.console.print("[yellow]Processing conference data...[/yellow]")
            df = self.process_conference_data(path, csn)

            if not df.empty:
                self.console.print(f"[green]Found {len(df)} records to process[/green]")

                # Ask if subject lines should be included
                include_subject = self.get_yes_no_input("Include subject line column in the output?")

                if include_subject:
                    # Generate subject lines
                    self.console.print("[yellow]Generating subject lines...[/yellow]")
                    df = self.generate_subject_lines(df, csn)
                    self.console.print("[green]Subject lines generated successfully[/green]")
                else:
                    self.console.print("[blue]Subject line column will not be included.[/blue]")

                # Ask if user wants to split the output into files of 10000 rows each
                create_splits = self.get_yes_no_input("Do you want to split the output into files of 10000 rows each?")

                # If user doesn't want splits, check if splits directory exists and remove it
                if not create_splits:
                    splits_dir = os.path.join(os.getcwd(), "output", "splits")
                    if os.path.exists(splits_dir):
                        try:
                            # Check if directory is empty
                            if not os.listdir(splits_dir):
                                os.rmdir(splits_dir)
                                self.console.print(f"[blue]Removed empty splits directory[/blue]")
                            else:
                                # Remove all files in the directory
                                for file in os.listdir(splits_dir):
                                    file_path = os.path.join(splits_dir, file)
                                    if os.path.isfile(file_path):
                                        os.remove(file_path)
                                # Then remove the directory
                                os.rmdir(splits_dir)
                                self.console.print(f"[blue]Removed splits directory and its contents[/blue]")
                        except Exception as e:
                            self.console.print(f"[yellow]Could not remove splits directory: {str(e)}[/yellow]")

                # Ask for number of templates/chunks only if not creating splits
                n_temps = 1
                if not create_splits:
                    while True:
                        try:
                            n_temps_input = input("Enter number of templates/chunks (default: 1): ").strip()
                            if not n_temps_input:  # If empty, use default
                                n_temps = 1
                                break
                            n_temps = int(n_temps_input)
                            if n_temps <= 0:
                                self.console.print("[yellow]Please enter a positive number.[/yellow]")
                            else:
                                break
                        except ValueError:
                            self.console.print("[yellow]Please enter a valid number.[/yellow]")

                # Save output files
                self.console.print("[yellow]Saving output files...[/yellow]")
                total_files = self.save_output_files(df, csn, n_temps, create_splits, include_subject)

                if total_files > 0:
                    file_type = "split" if create_splits else "template"
                    self.console.print(f"[green]Successfully saved {len(df)} records to {total_files} {file_type} files[/green]")
                    self.console.print(f"[green]Total records processed: {len(df)}[/green]")

            else:
                self.console.print("[yellow]No records found to process[/yellow]")

            self.console.print("[green]Processing completed successfully![/green]")

        except Exception as e:
            self.console.print(f"[red]Error: {str(e)}[/red]")
            self.console.print("[red]Process terminated with errors.[/red]")


def main():
    """Main execution function"""
    processor = MathewsConferenceProcessor()
    processor.run_processing()


if __name__ == "__main__":
    main()
