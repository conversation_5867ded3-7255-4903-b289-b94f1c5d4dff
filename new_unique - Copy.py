import pandas as pd
import os
import re
import glob
from datetime import datetime

# Import rich_progress for gradient progress bars
try:
    import rich_progress
except ImportError:
    # Define a simple fallback if rich_progress is not available
    class FallbackProgress:
        def print_status(self, message, style=None):
            # style parameter is ignored in the fallback
            print(message)

        def create_progress_bar(self, total, description, color_scheme=None):
            # total, description, and color_scheme parameters are ignored in the fallback
            class DummyBar:
                def stop(self):
                    pass

            def update_func(step=1, message=None):
                # step and message parameters are ignored in the fallback
                pass

            return DummyBar(), update_func

    rich_progress = FallbackProgress()

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def extract_segment(path):
    """
    Extracts a segment from a path using a regular expression.

    Args:
        path: The path string.

    Returns:
        The extracted segment (string) or None if not found.
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"  # Matches alphanumeric, spaces, hyphens, and 4 digits
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        rich_progress.print_status(f"Found segment: {segment}", "success")
        return segment
    else:
        rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
        return None

def process_data(cluster_folder, newdata_folder, output_path, csn):
    """
    Reads data from CSV files in folders, finds unique emails, and saves the result.

    Args:
        cluster_folder: Path to the folder containing cluster CSV files.
        newdata_folder: Path to the folder containing newdata CSV files.
        output_path: Path to save the output CSV file.
        csn: The CSN string for the output filename.
    """
    try:
        # Find all CSV files in the cluster folder
        print_section("Processing Cluster Files")
        cluster_files = glob.glob(os.path.join(cluster_folder, "*.csv"))
        if not cluster_files:
            rich_progress.print_status(f"No CSV files found in cluster folder: {cluster_folder}", "error")
            raise FileNotFoundError(f"No CSV files found in cluster folder: {cluster_folder}")

        rich_progress.print_status(f"Found {len(cluster_files)} CSV files in cluster folder", "info")

        # Create a progress bar for reading cluster files
        cluster_bar, update_cluster = rich_progress.create_progress_bar(
            total=len(cluster_files),
            description="Reading cluster files",
            color_scheme="blue"
        )

        # Read each cluster file with progress tracking
        cluster_dfs = []
        for file in cluster_files:
            try:
                df = pd.read_csv(file)
                cluster_dfs.append(df)
                update_cluster(1, f"Read {os.path.basename(file)}")
            except Exception as e:
                rich_progress.print_status(f"Error reading {file}: {str(e)}", "error")
                update_cluster(1, f"Error with {os.path.basename(file)}")

        # Stop the progress bar
        cluster_bar.stop()

        # Concatenate all cluster CSV files into a single DataFrame
        df_cluster = pd.concat(cluster_dfs, ignore_index=True)
        rich_progress.print_status(f"Combined {len(cluster_dfs)} cluster files with {len(df_cluster)} total rows", "success")

        # Find all CSV files in the newdata folder
        print_section("Processing Newdata Files")
        newdata_files = glob.glob(os.path.join(newdata_folder, "*.csv"))
        if not newdata_files:
            rich_progress.print_status(f"No CSV files found in newdata folder: {newdata_folder}", "error")
            raise FileNotFoundError(f"No CSV files found in newdata folder: {newdata_folder}")

        rich_progress.print_status(f"Found {len(newdata_files)} CSV files in newdata folder", "info")

        # Create a progress bar for reading newdata files
        newdata_bar, update_newdata = rich_progress.create_progress_bar(
            total=len(newdata_files),
            description="Reading newdata files",
            color_scheme="green"
        )

        # Read each newdata file with progress tracking
        newdata_dfs = []
        for file in newdata_files:
            try:
                df = pd.read_csv(file)
                newdata_dfs.append(df)
                update_newdata(1, f"Read {os.path.basename(file)}")
            except Exception as e:
                rich_progress.print_status(f"Error reading {file}: {str(e)}", "error")
                update_newdata(1, f"Error with {os.path.basename(file)}")

        # Stop the progress bar
        newdata_bar.stop()

        # Concatenate all newdata CSV files into a single DataFrame
        df_newdata = pd.concat(newdata_dfs, ignore_index=True)
        rich_progress.print_status(f"Combined {len(newdata_dfs)} newdata files with {len(df_newdata)} total rows", "success")

    except FileNotFoundError as e:
        rich_progress.print_status(f"Error: {e}", "error")
        return
    except pd.errors.EmptyDataError:
        rich_progress.print_status("Error: One or more of the input files are empty.", "error")
        return
    except pd.errors.ParserError:
        rich_progress.print_status("Error: There was an error parsing one or more of the input files.", "error")
        return
    except Exception as e:
        rich_progress.print_status(f"An unexpected error occurred: {e}", "error")
        return

    # Check if 'Email' column exists in both DataFrames
    print_section("Validating Data")
    if 'Email' not in df_cluster.columns or 'Email' not in df_newdata.columns:
        rich_progress.print_status("Error: 'Email' column not found in one or both of the input files.", "error")
        return

    rich_progress.print_status("Finding unique emails...", "info")
    df_new_uniq = df_newdata[~df_newdata.Email.isin(df_cluster.Email)]
    rich_progress.print_status(f"Found {len(df_new_uniq)} unique emails", "success")

    # Check if 'Name' or 'Author Name' column exists
    if 'Author Name' in df_new_uniq.columns:
        # If 'Author Name' exists, rename it to 'Name' for consistency
        df_new_uniq.rename(columns={'Author Name': 'Name'}, inplace=True)
        rich_progress.print_status("Renamed 'Author Name' column to 'Name'", "info")
    elif 'Name' not in df_new_uniq.columns:
        rich_progress.print_status("Error: Neither 'Name' nor 'Author Name' column found in the newdata file.", "error")
        return

    # Check if 'Email' column exists
    if 'Email' not in df_new_uniq.columns:
        rich_progress.print_status("Error: 'Email' column not found in the newdata file.", "error")
        return

    column_list = ['Name', 'Email']
    rich_progress.print_status(f"Using columns: {', '.join(column_list)}", "info")

    # Create the "new_unique" subfolder within the output path
    print_section("Saving Output")
    new_unique_folder = os.path.join(output_path, "new_unique")
    os.makedirs(new_unique_folder, exist_ok=True)  # Create the directory if it doesn't exist
    rich_progress.print_status(f"Created output directory: {new_unique_folder}", "info")

    output_file = os.path.join(new_unique_folder, f"{csn}_new-unique.csv")
    df_new_uniq.to_csv(output_file, columns=column_list, encoding='utf-8-sig', index=False)

    rich_progress.print_status(f"Number of unique rows: {len(df_new_uniq)}", "success")
    rich_progress.print_status(f"Data saved to: {output_file}", "success")

def main():
    """
    Main function to run the script.
    """
    try:
        # Print welcome header
        print_header("New Unique Email Finder")

        # Get input path
        print_section("Input Path")
        path = input("Enter Path to Save: ")

        # Extract segment from path
        print_section("Conference Segment Detection")
        csn = extract_segment(path)

        # If segment extraction failed, prompt user for manual input
        if not csn:
            print_section("Manual Input Required")
            rich_progress.print_status("Proceeding with manual input since segment couldn't be detected from path.", "info")
            csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ").strip()

            # Validate that the user provided a non-empty input
            while not csn:
                rich_progress.print_status("Conference segment name cannot be empty.", "warning")
                csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ").strip()

            rich_progress.print_status(f"Using manually entered segment: {csn}", "success")

        # Get input folders
        print_section("Input Folders")
        cluster_folder = input("Enter Path of Cluster Folder: ")
        rich_progress.print_status(f"Cluster folder: {cluster_folder}", "info")

        newdata_folder = input("Enter Path of Newdata Folder: ")
        rich_progress.print_status(f"Newdata folder: {newdata_folder}", "info")

        # Process the data
        process_data(cluster_folder, newdata_folder, path, csn)

        # Print completion message
        print_header("Processing Completed Successfully!")
        rich_progress.print_status(f"Conference segment: {csn}", "success")
        rich_progress.print_status(f"Output path: {path}", "info")

    except KeyboardInterrupt:
        rich_progress.print_status("\nOperation cancelled by user (Ctrl+C).", "warning")
    except Exception as e:
        rich_progress.print_status(f"\nAn unexpected error occurred: {str(e)}", "error")

if __name__ == "__main__":
    main()
