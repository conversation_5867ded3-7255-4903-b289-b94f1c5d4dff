#!/usr/bin/env python3
"""
CSV Backup Script for <PERSON> Bounces and Unsubs Directory
Backs up all CSV files from H:\<PERSON> Bounces and Unsubs\<PERSON> Bounces and Unsubs
Creates both individual file backups and a compressed archive
"""

import os
import shutil
import zipfile
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, track
from rich.table import Table
from rich.panel import Panel
import glob

class CSVBackupManager:
    def __init__(self):
        self.console = Console()
        self.source_dir = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.date_str = date.today().strftime("%Y-%m-%d")
        
    def validate_source_directory(self):
        """Validate that the source directory exists and contains CSV files."""
        if not os.path.exists(self.source_dir):
            self.console.print(f"[red]Error: Source directory not found: {self.source_dir}[/red]")
            return False
            
        csv_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
        if not csv_files:
            self.console.print(f"[yellow]Warning: No CSV files found in {self.source_dir}[/yellow]")
            return False
            
        self.console.print(f"[green]✓ Found {len(csv_files)} CSV files to backup[/green]")
        return True
    
    def create_backup_directories(self):
        """Create backup directories with timestamp."""
        # Create main backup directory
        backup_base = os.path.join(self.source_dir, "backups")
        os.makedirs(backup_base, exist_ok=True)
        
        # Create timestamped backup directory
        self.backup_dir = os.path.join(backup_base, f"backup_{self.timestamp}")
        os.makedirs(self.backup_dir, exist_ok=True)
        
        self.console.print(f"[blue]Created backup directory: {self.backup_dir}[/blue]")
        return self.backup_dir
    
    def backup_individual_files(self):
        """Create individual backups of each CSV file."""
        csv_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
        backup_stats = {
            'success': 0,
            'errors': 0,
            'total_size': 0
        }
        
        self.console.print("\n[cyan]Creating individual file backups...[/cyan]")
        
        for csv_file in track(csv_files, description="Backing up files..."):
            try:
                file_path = Path(csv_file)
                backup_filename = f"{file_path.stem}_{self.timestamp}{file_path.suffix}"
                backup_path = os.path.join(self.backup_dir, backup_filename)
                
                # Copy file with metadata
                shutil.copy2(csv_file, backup_path)
                
                # Get file size
                file_size = os.path.getsize(csv_file)
                backup_stats['total_size'] += file_size
                backup_stats['success'] += 1
                
                self.console.print(f"  ✓ {file_path.name} → {backup_filename}")
                
            except Exception as e:
                backup_stats['errors'] += 1
                self.console.print(f"  ✗ Error backing up {file_path.name}: {str(e)}")
        
        return backup_stats
    
    def create_zip_archive(self):
        """Create a compressed ZIP archive of all CSV files."""
        zip_filename = f"Master_CSV_Backup_{self.date_str}.zip"
        zip_path = os.path.join(self.backup_dir, zip_filename)
        
        self.console.print(f"\n[cyan]Creating ZIP archive: {zip_filename}[/cyan]")
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                csv_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
                
                for csv_file in track(csv_files, description="Compressing files..."):
                    file_path = Path(csv_file)
                    # Add file to zip with just the filename (no path)
                    zipf.write(csv_file, file_path.name)
                    
            # Get zip file size
            zip_size = os.path.getsize(zip_path)
            zip_size_mb = zip_size / (1024 * 1024)
            
            self.console.print(f"[green]✓ ZIP archive created: {zip_filename} ({zip_size_mb:.2f} MB)[/green]")
            return zip_path
            
        except Exception as e:
            self.console.print(f"[red]✗ Error creating ZIP archive: {str(e)}[/red]")
            return None
    
    def verify_backups(self, backup_stats):
        """Verify backup integrity by comparing file counts and sizes."""
        self.console.print("\n[cyan]Verifying backup integrity...[/cyan]")
        
        # Count original files
        original_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
        backup_files = glob.glob(os.path.join(self.backup_dir, "*.csv"))
        
        # Exclude the zip file from backup count
        backup_csv_count = len([f for f in backup_files if not f.endswith('.zip')])
        
        if len(original_files) == backup_csv_count:
            self.console.print("[green]✓ File count verification passed[/green]")
        else:
            self.console.print(f"[red]✗ File count mismatch: {len(original_files)} original vs {backup_csv_count} backup[/red]")
        
        return len(original_files) == backup_csv_count
    
    def generate_backup_report(self, backup_stats, zip_path):
        """Generate a detailed backup report."""
        # Create summary table
        table = Table(title="Backup Summary Report")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Backup Date", self.date_str)
        table.add_row("Backup Time", self.timestamp)
        table.add_row("Source Directory", self.source_dir)
        table.add_row("Backup Directory", self.backup_dir)
        table.add_row("Files Successfully Backed Up", str(backup_stats['success']))
        table.add_row("Files with Errors", str(backup_stats['errors']))
        table.add_row("Total Data Size", f"{backup_stats['total_size'] / (1024*1024):.2f} MB")
        
        if zip_path and os.path.exists(zip_path):
            zip_size = os.path.getsize(zip_path) / (1024*1024)
            table.add_row("ZIP Archive Size", f"{zip_size:.2f} MB")
            table.add_row("Compression Ratio", f"{(1 - zip_size*1024*1024/backup_stats['total_size'])*100:.1f}%")
        
        self.console.print(table)
        
        # Save report to file
        report_path = os.path.join(self.backup_dir, f"backup_report_{self.timestamp}.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"CSV Backup Report\n")
            f.write(f"================\n\n")
            f.write(f"Backup Date: {self.date_str}\n")
            f.write(f"Backup Time: {self.timestamp}\n")
            f.write(f"Source Directory: {self.source_dir}\n")
            f.write(f"Backup Directory: {self.backup_dir}\n")
            f.write(f"Files Successfully Backed Up: {backup_stats['success']}\n")
            f.write(f"Files with Errors: {backup_stats['errors']}\n")
            f.write(f"Total Data Size: {backup_stats['total_size'] / (1024*1024):.2f} MB\n")
            
            if zip_path and os.path.exists(zip_path):
                zip_size = os.path.getsize(zip_path) / (1024*1024)
                f.write(f"ZIP Archive Size: {zip_size:.2f} MB\n")
                f.write(f"Compression Ratio: {(1 - zip_size*1024*1024/backup_stats['total_size'])*100:.1f}%\n")
        
        self.console.print(f"[blue]Report saved to: {report_path}[/blue]")
    
    def run_backup(self):
        """Execute the complete backup process."""
        self.console.print(Panel.fit(
            "[bold blue]CSV Backup Manager[/bold blue]\n"
            f"Source: {self.source_dir}",
            border_style="blue"
        ))
        
        # Validate source directory
        if not self.validate_source_directory():
            return False
        
        # Create backup directories
        self.create_backup_directories()
        
        # Backup individual files
        backup_stats = self.backup_individual_files()
        
        # Create ZIP archive
        zip_path = self.create_zip_archive()
        
        # Verify backups
        verification_passed = self.verify_backups(backup_stats)
        
        # Generate report
        self.generate_backup_report(backup_stats, zip_path)
        
        # Final status
        if backup_stats['errors'] == 0 and verification_passed:
            self.console.print(Panel.fit(
                "[bold green]✓ Backup completed successfully![/bold green]\n"
                f"All {backup_stats['success']} CSV files backed up",
                border_style="green"
            ))
            return True
        else:
            self.console.print(Panel.fit(
                "[bold yellow]⚠ Backup completed with issues[/bold yellow]\n"
                f"Success: {backup_stats['success']}, Errors: {backup_stats['errors']}",
                border_style="yellow"
            ))
            return False

def main():
    """Main function to run the backup process."""
    backup_manager = CSVBackupManager()
    
    try:
        success = backup_manager.run_backup()
        if success:
            print("\nBackup process completed successfully!")
        else:
            print("\nBackup process completed with some issues. Check the report for details.")
            
    except KeyboardInterrupt:
        print("\n\nBackup process interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error during backup: {str(e)}")

if __name__ == "__main__":
    main()
