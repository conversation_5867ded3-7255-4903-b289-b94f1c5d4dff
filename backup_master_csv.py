#!/usr/bin/env python3
"""
CSV Backup Script for <PERSON> Bounces and Unsubs Directory
Backs up all CSV files from H:\<PERSON> Bounces and Unsubs\<PERSON> Bounces and Unsubs
Creates a backup folder with all CSV files and detailed reporting
"""

import os
import shutil
import zipfile
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, track
from rich.table import Table
from rich.panel import Panel
import glob

class CSVBackupManager:
    def __init__(self):
        self.console = Console()
        self.source_dir = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.date_str = date.today().strftime("%Y-%m-%d")
        
    def validate_source_directory(self):
        """Validate that the source directory exists and contains CSV files."""
        if not os.path.exists(self.source_dir):
            self.console.print(f"[red]Error: Source directory not found: {self.source_dir}[/red]")
            return False
            
        csv_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
        if not csv_files:
            self.console.print(f"[yellow]Warning: No CSV files found in {self.source_dir}[/yellow]")
            return False
            
        self.console.print(f"[green]✓ Found {len(csv_files)} CSV files to backup[/green]")
        return True
    
    def create_backup_directories(self):
        """Create backup directories with timestamp."""
        # Create backup directory directly in source directory
        backup_folder_name = f"CSV_Backup_{self.date_str}_{self.timestamp}"
        self.backup_dir = os.path.join(self.source_dir, backup_folder_name)
        os.makedirs(self.backup_dir, exist_ok=True)

        self.console.print(f"[blue]Created backup directory: {self.backup_dir}[/blue]")
        return self.backup_dir
    
    def backup_individual_files(self):
        """Create backups of each CSV file in the backup folder."""
        csv_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
        backup_stats = {
            'success': 0,
            'errors': 0,
            'total_size': 0
        }

        self.console.print("\n[cyan]Copying CSV files to backup folder...[/cyan]")

        for csv_file in track(csv_files, description="Backing up files..."):
            try:
                file_path = Path(csv_file)
                # Keep original filename in backup folder
                backup_path = os.path.join(self.backup_dir, file_path.name)

                # Copy file with metadata
                shutil.copy2(csv_file, backup_path)

                # Get file size
                file_size = os.path.getsize(csv_file)
                backup_stats['total_size'] += file_size
                backup_stats['success'] += 1

                self.console.print(f"  ✓ {file_path.name} → backup folder")

            except Exception as e:
                backup_stats['errors'] += 1
                self.console.print(f"  ✗ Error backing up {file_path.name}: {str(e)}")

        return backup_stats
    
    def create_backup_summary(self):
        """Create a summary file with backup information."""
        summary_filename = f"backup_summary_{self.timestamp}.txt"
        summary_path = os.path.join(self.backup_dir, summary_filename)

        self.console.print(f"\n[cyan]Creating backup summary: {summary_filename}[/cyan]")

        try:
            # Get list of backed up files
            backup_files = glob.glob(os.path.join(self.backup_dir, "*.csv"))

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(f"CSV Backup Summary\n")
                f.write(f"==================\n\n")
                f.write(f"Backup Date: {self.date_str}\n")
                f.write(f"Backup Time: {self.timestamp}\n")
                f.write(f"Source Directory: {self.source_dir}\n")
                f.write(f"Backup Directory: {self.backup_dir}\n")
                f.write(f"Files Backed Up: {len(backup_files)}\n\n")
                f.write(f"Backed Up Files:\n")
                f.write(f"================\n")

                for backup_file in backup_files:
                    file_name = os.path.basename(backup_file)
                    file_size = os.path.getsize(backup_file) / 1024  # KB
                    f.write(f"- {file_name} ({file_size:.1f} KB)\n")

            self.console.print(f"[green]✓ Backup summary created: {summary_filename}[/green]")
            return summary_path

        except Exception as e:
            self.console.print(f"[red]✗ Error creating backup summary: {str(e)}[/red]")
            return None
    
    def verify_backups(self, backup_stats):
        """Verify backup integrity by comparing file counts and sizes."""
        self.console.print("\n[cyan]Verifying backup integrity...[/cyan]")

        # Count original files
        original_files = glob.glob(os.path.join(self.source_dir, "*.csv"))
        backup_files = glob.glob(os.path.join(self.backup_dir, "*.csv"))

        if len(original_files) == len(backup_files):
            self.console.print("[green]✓ File count verification passed[/green]")
        else:
            self.console.print(f"[red]✗ File count mismatch: {len(original_files)} original vs {len(backup_files)} backup[/red]")

        # Verify individual file sizes
        size_match = True
        for original_file in original_files:
            original_name = os.path.basename(original_file)
            backup_file = os.path.join(self.backup_dir, original_name)

            if os.path.exists(backup_file):
                original_size = os.path.getsize(original_file)
                backup_size = os.path.getsize(backup_file)
                if original_size != backup_size:
                    self.console.print(f"[red]✗ Size mismatch for {original_name}[/red]")
                    size_match = False
            else:
                self.console.print(f"[red]✗ Missing backup file: {original_name}[/red]")
                size_match = False

        if size_match:
            self.console.print("[green]✓ File size verification passed[/green]")

        return len(original_files) == len(backup_files) and size_match
    
    def generate_backup_report(self, backup_stats, summary_path):
        """Generate a detailed backup report."""
        # Create summary table
        table = Table(title="Backup Summary Report")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("Backup Date", self.date_str)
        table.add_row("Backup Time", self.timestamp)
        table.add_row("Source Directory", self.source_dir)
        table.add_row("Backup Directory", self.backup_dir)
        table.add_row("Files Successfully Backed Up", str(backup_stats['success']))
        table.add_row("Files with Errors", str(backup_stats['errors']))
        table.add_row("Total Data Size", f"{backup_stats['total_size'] / (1024*1024):.2f} MB")

        # Calculate backup folder size
        backup_folder_size = 0
        for root, dirs, files in os.walk(self.backup_dir):
            for file in files:
                file_path = os.path.join(root, file)
                backup_folder_size += os.path.getsize(file_path)

        table.add_row("Backup Folder Size", f"{backup_folder_size / (1024*1024):.2f} MB")

        self.console.print(table)

        # Save detailed report to file
        report_path = os.path.join(self.backup_dir, f"backup_report_{self.timestamp}.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"CSV Backup Report\n")
            f.write(f"================\n\n")
            f.write(f"Backup Date: {self.date_str}\n")
            f.write(f"Backup Time: {self.timestamp}\n")
            f.write(f"Source Directory: {self.source_dir}\n")
            f.write(f"Backup Directory: {self.backup_dir}\n")
            f.write(f"Files Successfully Backed Up: {backup_stats['success']}\n")
            f.write(f"Files with Errors: {backup_stats['errors']}\n")
            f.write(f"Total Data Size: {backup_stats['total_size'] / (1024*1024):.2f} MB\n")
            f.write(f"Backup Folder Size: {backup_folder_size / (1024*1024):.2f} MB\n")

        self.console.print(f"[blue]Report saved to: {report_path}[/blue]")
    
    def run_backup(self):
        """Execute the complete backup process."""
        self.console.print(Panel.fit(
            "[bold blue]CSV Backup Manager[/bold blue]\n"
            f"Source: {self.source_dir}\n"
            "Creates backup folder with all CSV files",
            border_style="blue"
        ))

        # Validate source directory
        if not self.validate_source_directory():
            return False

        # Create backup directories
        self.create_backup_directories()

        # Backup individual files
        backup_stats = self.backup_individual_files()

        # Create backup summary
        summary_path = self.create_backup_summary()

        # Verify backups
        verification_passed = self.verify_backups(backup_stats)

        # Generate report
        self.generate_backup_report(backup_stats, summary_path)

        # Final status
        if backup_stats['errors'] == 0 and verification_passed:
            self.console.print(Panel.fit(
                "[bold green]✓ Backup completed successfully![/bold green]\n"
                f"All {backup_stats['success']} CSV files copied to backup folder",
                border_style="green"
            ))
            return True
        else:
            self.console.print(Panel.fit(
                "[bold yellow]⚠ Backup completed with issues[/bold yellow]\n"
                f"Success: {backup_stats['success']}, Errors: {backup_stats['errors']}",
                border_style="yellow"
            ))
            return False

def main():
    """Main function to run the backup process."""
    backup_manager = CSVBackupManager()
    
    try:
        success = backup_manager.run_backup()
        if success:
            print("\nBackup process completed successfully!")
        else:
            print("\nBackup process completed with some issues. Check the report for details.")
            
    except KeyboardInterrupt:
        print("\n\nBackup process interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error during backup: {str(e)}")

if __name__ == "__main__":
    main()
