@echo off
title CSV Backup for <PERSON> Bounces and <PERSON>subs
color 0A

echo.
echo ================================================================
echo                CSV BACKUP UTILITY
echo ================================================================
echo.
echo This script will backup all CSV files from:
echo H:\Master Bounces and Unsubs\Master Bounces and Unsubs
echo.
echo The backup will be saved in a folder with today's date and time.
echo.
pause

echo.
echo Starting backup process...
echo.

REM Run the Python backup script
python backup_master_csv_simple.py

echo.
echo ================================================================
echo                BACKUP PROCESS COMPLETED
echo ================================================================
echo.
pause
